<configuration scan="false" scanPeriod="60 seconds" debug="false">
  <appender name="RollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${CATALINA_HOME:-./}/logs/fs-app.log</file>
    <!-- 可让每天产生一个日志文件，自动回滚 -->
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${CATALINA_HOME:-./}/logs/fs-app-%d{yyyyMMdd}.log.zip</fileNamePattern>
      <maxHistory>7</maxHistory>
    </rollingPolicy>
    <encoder>
      <!-- 日志中默认打印traceId和userId，方便定位问题,异常栈中去掉包含如下字符的行避免打印很多无用的信息-->
      <pattern>%d{HH:mm:ss} [%thread] %-5level %logger{12} %X{traceId} %X{userId} %msg%rEx{full,
        java.lang.Thread,
        javassist,
        sun.reflect,
        org.springframework,
        org.apache,
        $Proxy,
        java.net,
        java.io,
        javax.servlet,
        org.junit,
        com.mysql,
        com.sun,
        org.mybatis.spring,
        cglib,
        CGLIB,
        java.util.concurrent,
        okhttp,
        org.jboss,
        }%n
      </pattern>
    </encoder>
  </appender>
  <!-- 异步输出日志避免阻塞服务 -->
  <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
    <queueSize>512</queueSize>
    <appender-ref ref="RollingFile" />
  </appender>
  <logger name="druid.sql" level="INFO" />
  <logger name="org.springframework" level="WARN" />
  <logger name="org.apache" level="WARN" />

  <root level="info">
    <appender-ref ref="ASYNC" />
  </root>
</configuration>
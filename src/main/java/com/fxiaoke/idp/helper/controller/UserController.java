package com.fxiaoke.idp.helper.controller;

import com.fxiaoke.boot.autoconfigure.iam.model.SecurityUserDetails;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping
public class UserController {

  @GetMapping(value = "/userinfo", consumes = MediaType.ALL_VALUE)
  public ResponseEntity<SecurityUserDetails> userinfo() {
    Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
    return ResponseEntity.ok((SecurityUserDetails) authentication.getDetails());
  }

}

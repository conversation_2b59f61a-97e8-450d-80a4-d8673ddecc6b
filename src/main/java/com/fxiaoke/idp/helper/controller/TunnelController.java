package com.fxiaoke.idp.helper.controller;

import com.fxiaoke.idp.helper.model.DunShanAppAddress;
import com.fxiaoke.idp.helper.service.DunShanService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 盾山代理通道配置查询
 */
@RestController
@RequestMapping
@RequiredArgsConstructor
public class TunnelController {

  private final DunShanService dunShanService;

  @GetMapping("/public/tunnels")
  public ResponseEntity<List<DunShanAppAddress>> queryDunShanAppAddress(@RequestParam(name = "cache", required = false, defaultValue = "true") boolean cache) {
    if (!cache) {
      dunShanService.cleanCache();
    }
    return ResponseEntity.ok(dunShanService.queryDunShanAppAddress());
  }

}

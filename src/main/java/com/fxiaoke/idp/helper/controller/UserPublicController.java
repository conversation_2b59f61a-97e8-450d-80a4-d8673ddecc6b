package com.fxiaoke.idp.helper.controller;

import com.fxiaoke.boot.autoconfigure.iam.model.UserDetail;
import com.fxiaoke.idp.helper.model.UserPhoneValidateRequest;
import com.fxiaoke.idp.helper.model.UserSearchRequest;
import com.fxiaoke.idp.helper.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Objects;

/**
 * 公开不需要认证的用户相关API
 */
@RestController
@RequestMapping("/public")
@RequiredArgsConstructor
public class UserPublicController {

  private final UserService userService;

  @PostMapping("/validate-phone-details")
  public ResponseEntity<UserDetail> validateByPhone(@RequestBody UserPhoneValidateRequest request) {
    return ResponseEntity.ok(userService.validateByPhone(request));
  }

  @GetMapping("/users/{id}")
  public ResponseEntity<UserDetail> queryUserById(@PathVariable String id,
                                                  @RequestParam(name = "detail", required = false) boolean detail) {
    UserDetail userDetail = userService.queryUser(id, detail);
    return Objects.isNull(userDetail) ? ResponseEntity.notFound().build() : ResponseEntity.ok(userDetail);
  }

  /**
   * 用户精确搜索，根据字段优先级查询，虽然是多个字段，实际上只用到其中一个
   */
  @GetMapping("/users/exact-search")
  public ResponseEntity<UserDetail> userExactSearch(UserSearchRequest request) {
    return ResponseEntity.ok(userService.userExactSearch(request));
  }

  @GetMapping(value = "/update-users-task")
  public ResponseEntity<String> updateUserAttributes() {
    userService.updateUserAttributes();
    return ResponseEntity.ok("start update users");
  }

}

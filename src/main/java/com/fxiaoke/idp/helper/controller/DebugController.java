package com.fxiaoke.idp.helper.controller;

import java.util.Map;
import java.util.stream.Collectors;

import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 生成研发 Debug token，辅助日常研发
 */
@RestController
@RequestMapping
public class DebugController {

  @GetMapping(value = "/headers", consumes = MediaType.ALL_VALUE)
  public ResponseEntity<Map<String, String>> headers(@RequestHeader Map<String, String> headers) {
    return ResponseEntity.ok(headers.entrySet()
        .stream()
        .filter(this::filter)
        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue)));
  }

  private boolean filter(Map.Entry<String, String> e) {
    String key = e.getKey();
    return key.startsWith("x-") || key.equalsIgnoreCase("Authorization");
  }

}

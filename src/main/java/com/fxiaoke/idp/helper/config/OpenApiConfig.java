package com.fxiaoke.idp.helper.config;

import io.swagger.v3.oas.annotations.OpenAPIDefinition;
import io.swagger.v3.oas.annotations.info.Info;
import io.swagger.v3.oas.annotations.servers.Server;
import org.springframework.context.annotation.Configuration;

/**
 * OpenAPI (Swagger) 配置
 */
@Configuration
@OpenAPIDefinition(info = @Info(title = "IDP Helper API", version = "1.0.0", description = "IDP Helper服务API文档"), servers = {
    @Server(url = "http://fs-idp-helper.fstest.svc", description = "firstshare"),
    @Server(url = "http://fs-idp-helper.foneshare.svc", description = "foneshare")
})
public class OpenApiConfig {

}
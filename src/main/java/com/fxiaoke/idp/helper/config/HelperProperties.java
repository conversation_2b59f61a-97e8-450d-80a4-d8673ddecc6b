package com.fxiaoke.idp.helper.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Data
@Configuration
@ConfigurationProperties(prefix = "sharecrm.api.idp.helper")
public class HelperProperties {

  /**
   * 定时任务自动更新用户属性的开关
   */
  private boolean updateUserEnabled;

  /**
   * K8S 管理平台 API 地址
   */
  private String k8sAppUrl;

  /**
   * 配置中心 API Token
   */
  private String configToken;

  /**
   * 配置中心 Profile 候选值
   */
  private String profileCandidates = "cloud,foneshare";

  /**
   * 盾山应用配置，key 是应用名，value 是盾山tunnel配置列表
   */
  private Map<String, List<String>> dunShanAppConfigs = Map.of(
    "egress-proxy-service", List.of("egress-proxy-tunnel"));

  /**
   * 盾山Server端配置文件，key是配置文件名，value是配置文件包含的分组
   */
  private Map<String, List<String>> dunShanServerConfigs = Map.of(
    "spring-cloud-egress-proxy-service", List.of("cloud", "foneshare")
  );

  private List<String> dunShanHiddenPorts = List.of("80", "443", "9999");

  private Map<String, CloudInfo> clouds = Map.of();

  private Map<String, List<String>> appReportClusters = Map.of();

}

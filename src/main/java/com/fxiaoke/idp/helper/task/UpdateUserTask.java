package com.fxiaoke.idp.helper.task;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import com.fxiaoke.idp.helper.config.HelperProperties;
import com.fxiaoke.idp.helper.service.UserService;

@Slf4j
@Component
@RequiredArgsConstructor
public class UpdateUserTask {

  private final HelperProperties properties;
  private final UserService userService;

  @Scheduled(cron = "${sharecrm.api.update.user.task.cron:0 0 2 * * ?}")
  public void updateUser() {
    if (!properties.isUpdateUserEnabled()) {
      log.info("update user task is disabled.");
      return;
    }
    // 补充用户数据
    userService.updateUserAttributes();
  }

}

package com.fxiaoke.idp.helper.model;

import io.protostuff.Tag;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 汇总应用情况，上报到 biz log
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AppReportBizLog {

  /**
   * 日志类型是generic-biz-log的要求，必须全局唯一，不能随便变
   */
  @Tag(1)
  private String logType = "app-report";

  @Tag(2)
  private long stamp;
  /**
   * 应用名称
   */
  @Tag(3)
  private String app;
  @Tag(4)
  private String serverIp;
  @Tag(5)
  private String profile;
  @Tag(6)
  private String ea;
  @Tag(7)
  private String tenantId;

  /**
   * 集群名称
   */
  @Tag(51)
  private String cluster;

  /**
   * 命名空间
   */
  @Tag(52)
  private String namespace;

  /**
   * 应用级别 (L0, L1, L2, L3)
   */
  @Tag(53)
  private String level;

  /**
   * 主要负责人
   */
  @Tag(54)
  private String mainOwner;

  /**
   * 部门leader
   */
  @Tag(55)
  private String leader;

  /**
   * 所属部门
   */
  @Tag(56)
  private String department;

  /**
   * Leader 所属部门
   */
  @Tag(57)
  private String leaderDepartment;

  @Tag(58)
  private String javaVersion;

  @Tag(59)
  private String tomcatVersion;

  @Tag(60)
  private String vendor;

  /**
   * 当前运行副本数
   */
  @Tag(151)
  private Integer running;

  /**
   * 发布系统配置的副本数
   */
  @Tag(152)
  private Integer replicas;

  /**
   * Git仓库URL (多个用逗号分隔)
   */
  @Tag(153)
  private String gitUrls;

  /**
   * 模块名称 (多个用逗号分隔)
   */
  @Tag(154)
  private String modules;

  /**
   * 上下文路径 (多个用逗号分隔)
   */
  @Tag(155)
  private String contextPaths;

}


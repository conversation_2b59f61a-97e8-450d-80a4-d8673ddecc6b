package com.fxiaoke.idp.helper.model;

import lombok.Data;

import java.util.List;

/**
 * K8s应用信息数据
 */
@Data
public class K8sApp {
  /**
   * 应用ID
   */
  private Integer id;

  /**
   * 应用名称
   */
  private String name;

  /**
   * 应用备注/描述
   */
  private String remark;

  /**
   * 所属部门
   */
  private String department;

  /**
   * 组织列表
   */
  private List<String> orgs;

  /**
   * 应用级别 (L0, L1, L2, L3)
   */
  private String level;

  /**
   * 主要负责人
   */
  private String mainOwner;

  /**
   * 负责人列表
   */
  private List<String> owners;

  /**
   * 应用分类
   */
  private String category;
}

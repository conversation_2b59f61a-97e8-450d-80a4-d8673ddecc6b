package com.fxiaoke.idp.helper.model;

import java.time.Duration;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 盾山应用地址数据
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DunShanAppAddress extends K8sAppAddress {
  /**
   * 方向，从哪个云到哪个云
   */
  private String direction;
  private String tunnelName;
  private String description;
  private String local;
  private String tunnel;
  /**
   * 目标地址的名字
   */
  private String target;
  /**
   * 目标地址的真实地址
   */
  private String targetAddress;
  private String proxy;
  private Duration connectTimeout;
  private Duration readTimeout;
  private Duration connectionLostTimeout;
  private Integer readBytesPerSecond;
  private Integer writeBytesPerSecond;
}

package com.fxiaoke.idp.helper.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.jbosslog.JBossLog;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;

@JBossLog
@UtilityClass
public class PinyinUtils {

  private static final HanyuPinyinOutputFormat outputFormat = pinyinOutputFormat();

  private static HanyuPinyinOutputFormat pinyinOutputFormat() {
    HanyuPinyinOutputFormat outputFormat = new HanyuPinyinOutputFormat();
    // 默认小写
    outputFormat.setCaseType(HanyuPinyinCaseType.LOWERCASE);
    // 不显示拼音的声调
    outputFormat.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
    return outputFormat;
  }

  public static String pinyin(String displayName) {
    StringBuilder sb = new StringBuilder();
    for (char c : displayName.toCharArray()) {
      try {
        // 只留中文部分，英文部分丢弃
        if (Character.toString(c).matches("[\\u4E00-\\u9FA5]+")) {
          sb.append(PinyinHelper.toHanyuPinyinStringArray(c, outputFormat)[0]);
        }
      } catch (Exception e) {
        log.error("Bad PinyinOutputFormatCombination", e);
      }
    }
    return sb.toString();
  }

}

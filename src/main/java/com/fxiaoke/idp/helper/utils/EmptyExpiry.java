package com.fxiaoke.idp.helper.utils;

import com.github.benmanes.caffeine.cache.Expiry;
import org.checkerframework.checker.index.qual.NonNegative;

import java.time.Duration;
import java.util.function.Function;

public class EmptyExpiry<K, V> implements Expiry<K, V> {

  private final Duration emptyTimeout;
  private final Duration cacheTimeout;
  private final Function<V, Boolean> isEmpty;

  public EmptyExpiry(Duration emptyTimeout, Duration cacheTimeout, Function<V, Boolean> isEmpty) {
    this.emptyTimeout = emptyTimeout;
    this.cacheTimeout = cacheTimeout;
    this.isEmpty = isEmpty;
  }

  @Override
  public long expireAfterCreate(K key, V value, long currentTime) {
    return isEmpty.apply(value) ? emptyTimeout.toNanos() : cacheTimeout.toNanos();
  }

  @Override
  public long expireAfterUpdate(K key, V value, long currentTime, @NonNegative long currentDuration) {
    return currentDuration;
  }

  @Override
  public long expireAfterRead(K key, V value, long currentTime, @NonNegative long currentDuration) {
    return currentDuration;
  }
}

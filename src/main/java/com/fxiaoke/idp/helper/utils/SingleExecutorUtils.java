package com.fxiaoke.idp.helper.utils;

import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 单线程ThreadPoolExecutor异步执行任务，注意任务超出后会直接丢弃，只能处理允许丢弃的任务
 */
@Slf4j
@UtilityClass
public class SingleExecutorUtils {

  private static final ExecutorService executor = new ThreadPoolExecutor(1, 4, 10, TimeUnit.MINUTES,
    new ArrayBlockingQueue<>(10000), (r, exec) -> log.warn("single executor dropped task: {} {}.", r, exec));

  public static void execute(Runnable task) {
    executor.execute(task);
  }

}

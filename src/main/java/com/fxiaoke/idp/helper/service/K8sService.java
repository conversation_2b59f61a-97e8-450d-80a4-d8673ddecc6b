package com.fxiaoke.idp.helper.service;

import com.fxiaoke.boot.autoconfigure.iam.model.UserDetail;
import com.fxiaoke.idp.helper.config.HelperProperties;
import com.fxiaoke.idp.helper.model.AppReportBizLog;
import com.fxiaoke.idp.helper.model.K8sApiResponse;
import com.fxiaoke.idp.helper.model.K8sApp;
import com.fxiaoke.idp.helper.model.K8sAppAddress;
import com.fxiaoke.idp.helper.model.K8sDeployment;
import com.fxiaoke.idp.helper.utils.Constants;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.ps.ProtostuffUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 纷享K8S管理平台接口
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class K8sService {

  private static final Pattern OPENJDK_PATTERN = Pattern.compile("openjdk(\\d+)");

  private static final Pattern DRAGONWELL_PATTERN = Pattern.compile("dragonwell(\\d+)");

  private final K8sClient k8sClient;

  private final HelperProperties properties;

  private final FsUserService fsUserService;

  /**
   * 查询应用列表
   *
   * @return 应用列表信息
   */
  public List<K8sApp> queryAppList() {
    try {
      K8sApiResponse<K8sApp> rs = k8sClient.getAppList();
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app list failed", e);
      return List.of();
    }
  }

  /**
   * 根据应用名称查询应用地址信息
   *
   * @param app 应用名称
   * @return 应用地址信息
   */
  public List<K8sAppAddress> queryAppAddress(String app) {
    try {
      K8sApiResponse<K8sAppAddress> rs = k8sClient.getAppAddress(app);
      log.info("query app address result, app: {}, message: {}", app, rs.getMessage());
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s app address failed, app: {}", app, e);
      return List.of();
    }
  }

  /**
   * 查询部署列表
   *
   * @param cluster   集群名称
   * @param namespace 命名空间
   * @return 部署列表信息
   */
  public List<K8sDeployment> queryDeploymentList(String cluster, String namespace) {
    try {
      K8sApiResponse<K8sDeployment> rs = k8sClient.getDeploymentList(cluster, namespace);
      return Objects.requireNonNullElse(rs.getData(), List.of());
    } catch (Exception e) {
      log.warn("query k8s deployment list failed, cluster: {}, namespace: {}", cluster, namespace, e);
      return List.of();
    }
  }

  public void doReport() {
    List<K8sApp> apps = queryAppList();
    if (CollectionUtils.isEmpty(apps)) {
      log.warn("query k8s app list failed, skip task");
      return;
    }
    properties.getAppReportClusters()
      .forEach((cluster, namespaces) -> namespaces.forEach(namespace -> {
        List<K8sDeployment> deployments = queryDeploymentList(cluster, namespace);
        saveDeploymentBizLog(apps, deployments);
      }));
  }

  private void saveDeploymentBizLog(List<K8sApp> apps, List<K8sDeployment> deployments) {
    if (CollectionUtils.isEmpty(deployments)) {
      log.warn("query k8s deployment list failed, skip task");
      return;
    }
    deployments.forEach(deploy -> {
      if (deploy.getReplicas() < 1 || deploy.getPipelineReplicas() < 1) {
        return;
      }
      AppReportBizLog biz = new AppReportBizLog();
      // 设置基础信息
      biz.setEa("fs");
      biz.setTenantId("1");
      biz.setServerIp(Constants.IP);
      biz.setApp(deploy.getName());
      biz.setProfile(deploy.getNamespace());
      biz.setStamp(System.currentTimeMillis());

      // 设置部署信息
      biz.setCluster(deploy.getCluster());
      biz.setNamespace(deploy.getNamespace());
      biz.setRunning(deploy.getPipelineReplicas());
      biz.setReplicas(deploy.getReplicas());
      // 解析并设置Java版本和Tomcat版本
      parseAndSetVersions(biz, deploy.getContainer0Image());
      trySetAppInfo(apps, deploy, biz);
      // 发送BizLog
      BizLogClient.send("generic-biz-log", ProtostuffUtil.serializer(biz));
    });
  }

  private void trySetAppInfo(List<K8sApp> apps, K8sDeployment deploy, AppReportBizLog biz) {
    apps.stream()
      .filter(app -> app.getName().equals(deploy.getName()))
      .findFirst()
      .ifPresent(k8sApp -> {
        // 设置应用相关信息
        biz.setDepartment(k8sApp.getDepartment());
        biz.setLevel(k8sApp.getLevel());
        String owner = k8sApp.getMainOwner();
        biz.setMainOwner(owner);
        UserDetail userDetail = fsUserService.queryUserByName(owner);
        if (Objects.nonNull(userDetail) && Objects.nonNull(userDetail.getLeader())) {
          biz.setLeader(userDetail.getLeader().getUsername());
          String mainDepartment = mainDepartment(userDetail.getLeader().getDepartment());
          biz.setLeaderDepartment(mainDepartment);
          biz.setDepartment(StringUtils.defaultIfEmpty(biz.getDepartment(), mainDepartment));
        }
      });
  }

  private String mainDepartment(String department) {
    if (StringUtils.isEmpty(department)) {
      return department;
    }
    return department.split(",")[0];
  }

  /**
   * 解析container0Image并设置Java版本和Tomcat版本
   */
  private void parseAndSetVersions(AppReportBizLog biz, String image) {
    if (StringUtils.isEmpty(image) || !image.contains("fs-tomcat")) {
      log.info("ignore image:{}", image);
      return;
    }
    try {
      // 解析Tomcat版本
      biz.setTomcatVersion(parseTomcatVersion(image));

      Matcher openjdkMatcher = OPENJDK_PATTERN.matcher(image);
      if (openjdkMatcher.find()) {
        biz.setJavaVersion(openjdkMatcher.group(1));
        biz.setVendor("openjdk");
        return;
      }
      Matcher dragonwellMatcher = DRAGONWELL_PATTERN.matcher(image);
      if (dragonwellMatcher.find()) {
        biz.setJavaVersion(dragonwellMatcher.group(1));
        biz.setVendor("dragonwell");
      }
    } catch (Exception e) {
      log.warn("Failed to parse versions from container image: {}", image, e);
    }
  }

  /**
   * 从容器镜像字符串中解析Tomcat版本
   * 使用正则表达式匹配 fs-tomcat + 数字
   */
  private String parseTomcatVersion(String container0Image) {
    Pattern pattern = Pattern.compile("fs-tomcat(\\d+)");
    Matcher matcher = pattern.matcher(container0Image);
    return matcher.find() ? matcher.group(1) : null;
  }
}

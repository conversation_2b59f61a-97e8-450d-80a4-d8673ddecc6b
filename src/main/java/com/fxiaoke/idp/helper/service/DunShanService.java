package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.config.CloudInfo;
import com.fxiaoke.idp.helper.config.HelperProperties;
import com.fxiaoke.idp.helper.model.DunShanAppAddress;
import com.fxiaoke.idp.helper.model.K8sAppAddress;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.configuration2.INIConfiguration;
import org.apache.commons.configuration2.PropertiesConfiguration;
import org.apache.commons.configuration2.SubnodeConfiguration;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

import java.io.StringReader;
import java.time.Duration;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class DunShanService {

  private static final List<String> keys = List.of("local", "tunnel", "target", "proxy", "scheme", "headers",
    "tunnel-aliases", "connect-timeout", "read-timeout",
    "read-bytes-per-second", "write-bytes-per-second",
    "secure", "direction", "description");

  private static final String SERVERS_PREFIX = "egress.websocket.servers.";

  private final HelperProperties properties;
  private final K8sService k8sService;
  private final ConfigService configService;

  @CacheEvict(value = "dunShan", key = "'dunShan'")
  public void cleanCache() {
    log.info("clean cache: {}", "dunShan");
  }

  @Cacheable(value = "dunShan", key = "'dunShan'")
  public List<DunShanAppAddress> queryDunShanAppAddress() {

    List<String> dunShanHiddenPorts = properties.getDunShanHiddenPorts();

    Map<String, List<String>> appConfigs = properties.getDunShanAppConfigs();

    Map<String, Map<String, Map<String, String>>> iniMaps = new ConcurrentHashMap<>();

    Map<String, String> dunShanServers = parseDunShanServersMap();

    return appConfigs.keySet()
      .stream()
      .flatMap(app -> k8sService.queryAppAddress(app).stream())
      .filter(Predicate.not(appAddress -> dunShanHiddenPorts.contains(appAddress.getPort())))
      .map(this::copyBean)
      // 配置文件，第一个是tunnel配置，第二个Spring Boot配置
      .map(addr -> initDunShanTunnels(addr, iniMaps, dunShanServers))
      .toList();
  }

  private Map<String, String> parseDunShanServersMap() {
    Map<String, String> dunShanServers = new ConcurrentHashMap<>();
    // 尝试找到 target 真实地址
    properties.getDunShanServerConfigs()
      .forEach((name, profiles) ->
        profiles.forEach(e -> parseDunShanServers(e, name, dunShanServers)));
    return dunShanServers;
  }

  private DunShanAppAddress initDunShanTunnels(DunShanAppAddress addr, Map<String, Map<String, Map<String, String>>> iniMaps, Map<String, String> dunShanServers) {
    //根据应用名和namespace获取配置文件，第一个是tunnel配置，第二个Spring Boot配置
    List<String> configs = properties.getDunShanAppConfigs().get(addr.getApp());
    if (CollectionUtils.isEmpty(configs)) {
      log.warn("no config found for app: {}", addr.getApp());
      return addr;
    }
    String tunnel = configs.getFirst();
    String mapKey = addr.getNamespace() + tunnel;
    iniMaps.computeIfAbsent(mapKey, key -> parseTunnelIniConfig(addr, tunnel));
    iniMaps.get(mapKey)
      .entrySet()
      .stream()
      .filter(e -> matchPort(addr, e))
      .findFirst()
      .ifPresent(e -> addTunnelDetail(e, addr));

    // 尝试找到 target 真实地址
    addr.setTargetAddress(dunShanServers.get(Objects.requireNonNullElse(addr.getTarget(), "")));

    adapterDirection(addr);

    // 清空不对外开放的
    addr.setClusterOuterAddress(null);
    addr.setClusterInnerAddress(null);
    //name是端口的名字，目前没啥用
    addr.setName(null);
    return addr;
  }

  private void adapterDirection(DunShanAppAddress addr) {
    String namespace = addr.getNamespace();
    String tunnel = addr.getTunnel();
    if (StringUtils.isEmpty(namespace) || StringUtils.isEmpty(tunnel)) {
      return;
    }
    Collection<CloudInfo> clouds = properties.getClouds().values();
    String from = clouds
      .stream()
      .filter(cloudInfo -> cloudInfo.getNamespaces().stream().anyMatch(namespace::contains))
      .map(CloudInfo::getName)
      .findFirst()
      .orElse(namespace);
    String to = clouds.stream()
      .filter(cloudInfo -> StringUtils.contains(tunnel, cloudInfo.getTunnel()))
      .map(CloudInfo::getName)
      .findFirst()
      .orElse(tunnel);
    addr.setDirection("从" + from + "到" + to);
  }

  private void parseDunShanServers(String profile, String name, Map<String, String> dunShanServers) {
    String config = configService.getConfig(profile, name, false);
    PropertiesConfiguration properties = new PropertiesConfiguration();
    try (StringReader reader = new StringReader(config)) {
      properties.read(reader);
      properties.getKeys().forEachRemaining(fullKey -> {
        if (fullKey.startsWith(SERVERS_PREFIX)) {
          String serverName = fullKey.substring(SERVERS_PREFIX.length());
          String serverAddress = properties.getString(fullKey);
          log.info("dun shan server address: {}, {}", serverName, serverAddress);
          dunShanServers.put(serverName, serverAddress);
        }
      });
    } catch (Exception e) {
      log.error("cannot load config: {}, {} ", profile, name, e);
    }
  }

  private void addTunnelDetail(Map.Entry<String, Map<String, String>> e, DunShanAppAddress addr) {
    Map<String, String> options = e.getValue();
    addr.setTunnelName(e.getKey());
    addr.setLocal(options.get("local"));
    addr.setDescription(options.get("description"));
    addr.setTunnel(options.get("tunnel"));
    addr.setTarget(options.get("target"));
    addr.setProxy(options.get("proxy"));
    int connectTimeout = MapUtils.getIntValue(options, "connect-timeout", 10_000);
    int readTimeout = MapUtils.getIntValue(options, "read-timeout", 60_000);
    int connectionLostTimeout = MapUtils.getIntValue(options, "connection-lost-timeout", 60);
    int readBytesPerSecond = MapUtils.getIntValue(options, "read-bytes-per-second", 1024 * 1024);
    int writeBytesPerSecond = MapUtils.getIntValue(options, "write-bytes-per-second", 1024 * 1024);
    addr.setConnectTimeout(Duration.ofSeconds(connectTimeout));
    addr.setReadTimeout(Duration.ofSeconds(readTimeout));
    addr.setConnectionLostTimeout(Duration.ofSeconds(connectionLostTimeout));
    addr.setReadBytesPerSecond(readBytesPerSecond);
    addr.setWriteBytesPerSecond(writeBytesPerSecond);
  }

  private boolean matchPort(DunShanAppAddress addr, Map.Entry<String, Map<String, String>> e) {
    String port = e.getValue().getOrDefault("local", "0:0").split(":")[1];
    return addr.getPort().equals(port);
  }

  @NotNull
  private Map<String, Map<String, String>> parseTunnelIniConfig(DunShanAppAddress addr, String tunnel) {
    String config = configService.getConfig(addr.getNamespace(), tunnel, true);
    if (StringUtils.isBlank(config)) {
      return Map.of();
    }
    INIConfiguration ini = new INIConfiguration();
    try (StringReader reader = new StringReader(config)) {
      ini.read(reader);
    } catch (Exception e) {
      log.error("cannot load config: {}, ", tunnel, e);
      return Map.of();
    }
    SubnodeConfiguration common = ini.getSection("common");
    //与common配置merge
    return ini.getSections()
      .stream()
      .filter(s -> !"common".equals(s))
      .collect(Collectors.toMap(Function.identity(),
        name -> parseConfig(ini.getSection(name), common))
      );
  }

  private Map<String, String> parseConfig(SubnodeConfiguration section, SubnodeConfiguration common) {
    Map<String, String> config = new HashMap<>();
    keys.forEach(k -> {
      String v = Optional.ofNullable(section.getString(k))
        .orElseGet(() -> common.getString(k, ""));
      if (!v.isEmpty()) {
        config.put(k, v);
      }
    });
    return config;
  }

  private DunShanAppAddress copyBean(K8sAppAddress addr) {
    DunShanAppAddress dest = new DunShanAppAddress();
    try {
      BeanUtils.copyProperties(dest, addr);
    } catch (Exception e) {
      log.error("copy k8s app address to dun shan app address failed, addr: {}", addr, e);
    }
    dest.setInnerAddress(joinAddress(addr.getClusterInnerAddress(), () -> String.join(".", addr.getName(), addr.getNamespace(), addr.getPort())));
    dest.setOuterAddress(joinAddress(addr.getClusterOuterAddress(), () -> "Node:" + addr.getNodePort()));
    return dest;
  }

  private String joinAddress(List<String> addr, Supplier<String> defaultValue) {
    return Optional.ofNullable(addr)
      .map(addresses -> String.join(",", addresses))
      .filter(Predicate.not(e -> e.equals("--")))
      .orElseGet(defaultValue);
  }
}

package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.config.HelperProperties;
import com.github.autoconf.admin.ConfigAdminClient;
import com.github.autoconf.admin.api.IConfigAdmin;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 纷享配置中心接口，读取配置文件
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ConfigService {

  private final HelperProperties properties;

  @Cacheable(value = "profileConfig")
  public String getConfig(String profile, String name, boolean resolveVariable) {
    try {
      IConfigAdmin admin = new ConfigAdminClient();
      String candidates = "foneshare".equals(profile) ? null : properties.getProfileCandidates();
      String token = properties.getConfigToken();
      log.debug("config: tk:{}, profile={}, name={}, candidates={}", token, profile, name, candidates);
      return admin.get(token, profile, name, candidates, resolveVariable);
    } catch (Exception e) {
      log.warn("failed to get config: profile={}, name={}", profile, name, e);
      return null;
    }
  }

}

package com.fxiaoke.idp.helper.service;

import com.facishare.organization.api.model.department.DepartmentDto;
import com.fxiaoke.boot.autoconfigure.iam.model.GroupDetail;
import com.fxiaoke.boot.autoconfigure.iam.model.UserDetail;
import com.fxiaoke.boot.autoconfigure.iam.service.KeycloakService;
import com.fxiaoke.idp.helper.model.UserPhoneValidateRequest;
import com.fxiaoke.idp.helper.model.UserSearchRequest;
import com.fxiaoke.idp.helper.utils.PinyinUtils;
import com.fxiaoke.idp.helper.utils.SingleExecutorUtils;
import jakarta.annotation.Nullable;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_DEPARTMENT;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_DEPARTMENT_IDS;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_DISPLAY_NAME;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_EMPLOYEE_ID;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_GROUP_DEPARTMENT_ID;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_GROUP_DEPARTMENT_ORDER;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_GROUP_PRINCIPAL_ID;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_LEADER_EMPLOYEE_ID;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_PHONE_NUMBER;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_PINYIN;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_SEARCH;
import static com.fxiaoke.boot.autoconfigure.iam.utils.KeycloakUtils.KEY_USER_POST;

@Slf4j
@Service
@AllArgsConstructor
public class UserService {

  private final KeycloakService keycloakService;
  private final FsUserService fsUserService;

  public void updateUserAttributes() {

    SingleExecutorUtils.execute(() -> {
      try {
        log.info("begin update users");
        keycloakService.listUsers().forEach(userDetail -> {
          try {
            // 补充用户数据
            tryUpdateUser(userDetail);
            // 补充Group数据
            tryJoinGroup(userDetail);
          } catch (Exception e) {
            log.warn("update user or group failed:{}", userDetail, e);
          }
        });
        log.info("end update users");
      } catch (Exception e) {
        log.warn("failed update users", e);
      }
    });

  }

  private void tryJoinGroup(UserDetail userDetail) {
    Map<String, List<String>> attributes = userDetail.getAttributes();
    String employeeId = firstAttribute(attributes, KEY_EMPLOYEE_ID);
    if (StringUtils.isBlank(employeeId)) {
      return;
    }
    UserDetail fsUser = fsUserService.queryUserByEmployeeId(employeeId);
    if (Objects.isNull(fsUser)) {
      log.info("fs user not found:{}", employeeId);
      return;
    }
    String departmentIds = fsUser.getDepartmentIds();
    if (StringUtils.isBlank(departmentIds)) {
      return;
    }
    String[] split = departmentIds.split(",");
    for (String id : split) {
      // 遍历成树形结构，创建组
      GroupDetail group = tryCreateGroups(Integer.parseInt(id));
      keycloakService.userJoinGroup(userDetail, group);
    }

  }

  private GroupDetail tryCreateGroups(Integer departmentId) {
    DepartmentDto department = fsUserService.queryDepartmentById(departmentId);
    GroupDetail group = new GroupDetail();
    group.setName(Objects.requireNonNull(department.getName()));
    group.setAttributes(Map.of(
      KEY_GROUP_DEPARTMENT_ID, List.of("" + department.getDepartmentId()),
      KEY_GROUP_PRINCIPAL_ID, List.of("" + department.getPrincipalId()),
      KEY_GROUP_DEPARTMENT_ORDER, List.of("" + department.getDepartmentOrder())));

    GroupDetail groupResponse = keycloakService.queryOrAddGroup(group);
    Integer parentId = department.parentId();
    if (Objects.nonNull(parentId) && parentId > 0) {
      GroupDetail parentGroupResponse = tryCreateGroups(parentId);
      keycloakService.moveGroup(groupResponse, parentGroupResponse);
    }
    return groupResponse;
  }

  private void tryUpdateUser(UserDetail user) {
    // 这些大部分都是从ldap同步过来的，只补充我们需要的
    try {
      Map<String, List<String>> attributes = user.getAttributes();

      Map<String, List<String>> update = new HashMap<>();
      addPinyinSearcher(attributes, update);
      addFsUserDetail(attributes, update);
      if (update.isEmpty()) {
        return;
      }
      keycloakService.updateUserAttributes(user, update);
      log.info("update user success:{}", user);
    } catch (Exception e) {
      log.warn("update user failed:{}", user, e);
    }

  }

  private void addFsUserDetail(Map<String, List<String>> attributes, Map<String, List<String>> update) {
    String employeeId = firstAttribute(attributes, KEY_EMPLOYEE_ID);
    if (StringUtils.isBlank(employeeId)) {
      return;
    }
    String leaderId = firstAttribute(attributes, KEY_LEADER_EMPLOYEE_ID);
    // 这是最后一个补充的数据，如果这个有了，下面的也都有了
    if (StringUtils.isNotBlank(leaderId)) {
      return;
    }

    UserDetail fsUser = fsUserService.queryUserByEmployeeId(employeeId);
    if (Objects.isNull(fsUser)) {
      log.info("fs user not found by id:{}", employeeId);
      return;
    }

    update.put(KEY_LEADER_EMPLOYEE_ID, List.of(StringUtils.defaultIfBlank(fsUser.getLeader().getEmployeeId(), "")));
    update.put(KEY_USER_POST, List.of(StringUtils.defaultIfBlank(fsUser.getPost(), "")));
    update.put(KEY_DEPARTMENT, List.of(StringUtils.defaultIfBlank(fsUser.getDepartment(), "")));
    update.put(KEY_DEPARTMENT_IDS, List.of(StringUtils.defaultIfBlank(fsUser.getDepartmentIds(), "")));

  }

  private void addPinyinSearcher(Map<String, List<String>> attributes, Map<String, List<String>> update) {
    String displayName = firstAttribute(attributes, KEY_DISPLAY_NAME);
    if (StringUtils.isEmpty(displayName)) {
      return;
    }
    String pinyin = firstAttribute(attributes, KEY_PINYIN);
    if (StringUtils.isEmpty(pinyin)) {
      pinyin = PinyinUtils.pinyin(displayName);
      update.put(KEY_PINYIN, List.of(pinyin));
    }
    String searcher = firstAttribute(attributes, KEY_SEARCH);
    if (StringUtils.isEmpty(searcher)) {
      update.put(KEY_SEARCH, List.of(searcherValue(displayName, pinyin)));
    }
  }

  private String searcherValue(String displayName, String pinyin) {
    return Objects.toString(displayName, "") + Objects.toString(pinyin, "");
  }

  private String firstAttribute(Map<String, List<String>> attributes, String key) {
    if (Objects.isNull(attributes)) {
      return null;
    }
    List<String> list = attributes.get(key);
    if (CollectionUtils.isNotEmpty(list)) {
      return list.getFirst();
    }
    return null;
  }

  public UserDetail validateByPhone(UserPhoneValidateRequest request) {
    return fsUserService.validateByPhone(request);
  }

  public @Nullable UserDetail queryUser(String id, boolean detail) {
    return keycloakService.queryUser(id, detail);
  }

  public UserDetail userExactSearch(UserSearchRequest request) {
    boolean detail = false;
    // 根据优先级逐个查询，查到为止
    if (StringUtils.isNotBlank(request.getId())) {
      return keycloakService.queryUser(request.getId(), detail);
    }
    if (StringUtils.isNotBlank(request.getUsername())) {
      return keycloakService.queryByUsername(request.getUsername());
    }
    if (StringUtils.isNotBlank(request.getDisplayName())) {
      return searchUserByAttributes(Map.of(KEY_DISPLAY_NAME, request.getDisplayName()));
    }
    if (StringUtils.isNotBlank(request.getEmail())) {
      return searchUserByAttributes(Map.of("email", request.getEmail()));
    }
    if (StringUtils.isNotBlank(request.getPhone())) {
      return searchUserByAttributes(Map.of(KEY_PHONE_NUMBER, request.getPhone()));
    }
    if (StringUtils.isNotBlank(request.getEmployeeId())) {
      return searchUserByAttributes(Map.of(KEY_EMPLOYEE_ID, request.getEmployeeId()));
    }
    return null;
  }

  private UserDetail searchUserByAttributes(Map<String, String> query) {
    return keycloakService.searchUserByAttributes(query, true)
      .stream()
      .findFirst()
      .orElse(null);
  }


}

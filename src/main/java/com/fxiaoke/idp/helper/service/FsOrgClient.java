package com.fxiaoke.idp.helper.service;

import com.facishare.organization.api.model.department.arg.GetAllDepartmentDtoArg;
import com.facishare.organization.api.model.department.arg.GetDepartmentDtoArg;
import com.facishare.organization.api.model.department.result.GetAllDepartmentDtoResult;
import com.facishare.organization.api.model.department.result.GetDepartmentDtoResult;
import com.facishare.organization.api.model.employee.arg.GetAllEmployeesDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeeDtoLeaderArg;
import com.facishare.organization.api.model.employee.arg.GetEmployeesDtoByNameArg;
import com.facishare.organization.api.model.employee.result.GetAllEmployeesDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoLeaderResult;
import com.facishare.organization.api.model.employee.result.GetEmployeeDtoResult;
import com.facishare.organization.api.model.employee.result.GetEmployeesDtoByNameResult;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 纷享自己的组织结构接口
 */
@Component
@FeignClient(value = "fsOrgClient", url = "${sharecrm.api.idp.helper.organization-url:http://fs-organization-provider.nsvc.foneshare.cn}")
public interface FsOrgClient {

  /**
   * 获取一个企业所有员工
   */
  @PostMapping(value = "/EmployeeProviderService/getAllEmployees")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetAllEmployeesDtoResult getAllEmployees(@RequestBody GetAllEmployeesDtoArg getAllEmployeesDtoArg);

  /**
   * 根据员工昵称获取对应的员工信息
   */
  @PostMapping(value = "/EmployeeProviderService/getEmployeesByName")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetEmployeesDtoByNameResult getEmployeesByName(@RequestBody GetEmployeesDtoByNameArg getEmployeesDtoByNameArg);

  /**
   * 获取员工Dto
   */
  @PostMapping(value = "/EmployeeProviderService/getEmployeeDto")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetEmployeeDtoResult getEmployeeDto(@RequestBody GetEmployeeDtoArg getEmployeeDtoArg);

  /**
   * 获取一个员工的上级
   */
  @PostMapping(value = "/EmployeeProviderService/getEmployeeDtoLeader")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetEmployeeDtoLeaderResult getEmployeeDtoLeader(@RequestBody GetEmployeeDtoLeaderArg getEmployeeDtoLeaderArg);

  /**
   * 获取企业下的所有部门
   */
  @PostMapping(value = "/DepartmentProviderService/getAllDepartmentDto")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetAllDepartmentDtoResult getAllDepartmentDto(@RequestBody GetAllDepartmentDtoArg arg);

  /**
   * 根据部门Id获取部门信息
   */
  @PostMapping(value = "/DepartmentProviderService/getDepartmentDto")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  GetDepartmentDtoResult getDepartmentDto(@RequestBody GetDepartmentDtoArg arg);
}

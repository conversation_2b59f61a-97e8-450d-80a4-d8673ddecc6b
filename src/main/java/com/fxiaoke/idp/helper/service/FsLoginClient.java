package com.fxiaoke.idp.helper.service;

import com.fxiaoke.idp.helper.model.FsVerifyPasswordArg;
import com.fxiaoke.idp.helper.model.FsVerifyPasswordResult;
import feign.Headers;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 纷享登录接口，比如按手机号密码
 */
@Component
@FeignClient(value = "fsLoginClient", url = "${sharecrm.api.idp.helper.user-login-url:http://fs-uc-provider/uc-provider}")
public interface FsLoginClient {

  @PostMapping(value = "/EmployeeLoginService/verifyEmployeePasswordByUserAccount")
  @Headers({"Content-Type: application/json;charset=UTF-8"})
  FsVerifyPasswordResult verifyUserAccount(FsVerifyPasswordArg arg);

}
